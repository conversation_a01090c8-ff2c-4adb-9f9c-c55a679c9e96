import re
from dataclasses import dataclass

import pandas as pd
import requests
from cachetools.func import ttl_cache
from tenacity import retry, retry_if_not_exception_type, stop_after_attempt, wait_fixed

from profits_pred.pyxis.utils.authorization.his_authorization import get_dynamic_token, decrypt
from profits_pred.pyxis.utils.config_center import parse_datasource_url


def snake_to_camel(text: str) -> str:
    parts = text.split("_")
    return parts[0].lower() + "".join(word.capitalize() for word in parts[1:])


def camel_to_snake(text: str) -> str:
    return re.sub(r"(?<!^)(?=[A-Z])", "_", text).lower()


class HisConfigCenterClientException(Exception):
    def __init__(self):
        super().__init__()


@dataclass(frozen=True)
class HisConfigCenterCredential:
    url: str
    static_token: str
    app_id: str
    deployment_unit: str
    region: str
    environment: str
    version: str = "1.0"


@ttl_cache(maxsize=128, ttl=300)
@retry(
    retry=retry_if_not_exception_type(HisConfigCenterClientException), stop=stop_after_attempt(3),
    wait=wait_fixed(1), reraise=True
)
def get_all_config(token_url: str, credential: HisConfigCenterCredential) -> dict:
    headers = {"Authorization": get_dynamic_token(token_url, credential.app_id, credential.static_token)}
    query_params = {
        "application_id": credential.app_id,
        "sub_application_id": credential.deployment_unit,
        "environment": credential.environment,
        "region": credential.region,
        "version": credential.version
    }
    response = requests.get(credential.url, headers=headers, params=query_params, timeout=1)

    if response.status_code != 200:
        raise HisConfigCenterClientException()

    return response.json()


class HisConfigCenterClient:

    def __init__(self, token_url, credential: HisConfigCenterCredential):
        super().__init__()
        self.credential = credential
        self.token_url = token_url

    def get_account_config(self) -> pd.DataFrame:
        """
        Retrieve the account configuration.
        """
        accounts = pd.DataFrame(get_all_config(self.token_url, self.credential)["j2c"])
        accounts.rename(columns=lambda x: camel_to_snake(x), inplace=True)
        return accounts

    def get_datasource_config(self) -> pd.DataFrame:
        """
        Retrieve the datasource configuration.
        """
        datasource = pd.DataFrame(get_all_config(self.token_url, self.credential)["datasource"])
        datasource.rename(columns=lambda x: camel_to_snake(x), inplace=True)
        return datasource

    def get_application_config(self) -> pd.DataFrame:
        """
        Retrieve the application configuration.
        """
        app_config = pd.DataFrame(get_all_config(self.token_url, self.credential)["app_config"])
        app_config.rename(columns=lambda x: camel_to_snake(x), inplace=True)
        return app_config

    def get_application_config_by_key(self, key: str) -> str:
        application_config = self.get_application_config()
        config = application_config.loc[application_config.config_type == key].iloc[0]
        return config.config_value

    def get_public_config(self) -> dict[str, str]:
        """
        Retrieve the public configuration.
        """
        return get_all_config(self.token_url, self.credential)["public_config"]

    def get_distributed_storage_config(self) -> pd.DataFrame:
        """
        Retrieve the distributed storage configuration.
        """
        distributed_storage_config = pd.DataFrame(
            get_all_config(self.token_url, self.credential)["distributed_storage"]
        )
        distributed_storage_config.rename(columns=lambda x: camel_to_snake(x), inplace=True)
        return distributed_storage_config

    def get_decrypted_accounts(self) -> pd.DataFrame:
        """
        Return all decrypted account configs for convenience, assume only password attribute has encrypted info.
        """
        accounts = self.get_account_config()
        accounts["password"] = accounts.apply(
            lambda x: decrypt(
                config_parts=x["config_parts"],
                work_key_cipher=x["work_key_cipher"],
                text=x["password"],
                encrypt_type=x["encrypt_type"]
            ),
            axis=1
        )
        return accounts[["j2c_name", "user", "password"]]

    def get_account_by_name(self, name: str) -> dict:
        accounts = self.get_decrypted_accounts()
        config = accounts.loc[accounts.j2c_name == name].iloc[0]
        return {
            "user": config.user,
            "password": config.password,
        }

    def get_decrypted_datasource(self) -> pd.DataFrame:
        """
        Return all decrypted datasource configs for convenience, assume only password attribute has encrypted info.
        """
        datasource = self.get_datasource_config()
        datasource["password"] = datasource.apply(
            lambda x: decrypt(
                config_parts=x["config_parts"],
                work_key_cipher=x["work_key_cipher"],
                text=x["password"],
                encrypt_type=x["encrypt_type"]
            ),
            axis=1
        )
        datasource.rename(columns={"d_b_type": "db_type"}, inplace=True)
        return datasource[
            [
                "name", "db_type", "jdbc_driver_class",
                "database_name", "url", "user", "password",
                "min_idle", "max_idle", "max_total", "initial_size",
                "time_between_eviction_runs_millis", "min_evictable_idle_time_millis",
                "max_conn_lifetime_millis", "max_wait_millis"
            ]
        ]

    def get_rdb_config_by_name(self, datasource_name: str) -> dict:
        """
        Make sure the config in the config center is correct such as
        initial_size, max_total, max_wait_millis, max_conn_lifetime_millis
        """
        data_source = self.get_decrypted_datasource()
        config = data_source.loc[data_source.name == datasource_name].iloc[0]
        rdb_config = parse_datasource_url.parse_rdb_url(config.url)
        return {
            "driver_name": rdb_config.driver,
            "user": config.user,
            "password": config.password,
            "host": rdb_config.host,
            "port": rdb_config.port,
            "database": rdb_config.database,
            "connect_args": rdb_config.connect_args,
            "pool_size": int(config.initial_size),
            "max_overflow": int(config.max_total) - int(config.initial_size),
            "pool_timeout": int(config.max_wait_millis) // 1000,
            "pool_recycle": int(config.max_conn_lifetime_millis) // 1000
        }

    def get_decrypted_distributed_storage(self) -> pd.DataFrame:
        """
        Return all decrypted distributed storage configs for convenience,
        assume only secret_key attribute has encrypted info.
        """
        distributed_storage = self.get_distributed_storage_config()
        distributed_storage["secret_key"] = distributed_storage.apply(
            lambda x: decrypt(
                config_parts=x["config_parts"],
                work_key_cipher=x["work_key_cipher"],
                text=x["secret_key"],
                encrypt_type=x["encrypt_type"]
            ),
            axis=1
        )
        return distributed_storage[["name", "storage_type", "endpoint", "bucket", "access_key", "secret_key"]]

    def get_obs_config_by_bucket(self, bucket: str) -> dict:
        distributed_storage = self.get_decrypted_distributed_storage()
        config = distributed_storage.loc[distributed_storage.name == bucket].iloc[0]
        return {
            "bucket": config.bucket, "access_key": config.access_key,
            "secret_key": config.secret_key, "endpoint": config.endpoint
        }
