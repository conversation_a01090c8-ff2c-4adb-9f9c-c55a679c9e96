import os

from src.utils.resource_loader import LOGGER as log
import pandas as pd

dimension_subcategory_dim = [
    'scenarios', 'bg_code',
    'bg_name', 'oversea_code',
    'oversea_desc', 'lv1_code', 'lv1_name',
    'lv2_code', 'lv2_name',
    'currency', 'dimension_subcategory_code',
    'dimension_subcategory_cn_name',
    'dimension_subcategory_en_name',
]
dimension_group_dim = [
    'scenarios', 'bg_code',
    'bg_name', 'oversea_code', 'oversea_desc',
    'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name',
    'currency', 'dimension_group_code', 'dimension_group_cn_name', 'dimension_group_en_name'
]
lv2_dim = [
    'scenarios', 'bg_code', 'bg_name',
    'oversea_code', 'oversea_desc',
    'lv1_code',
    'lv1_name',
    'lv2_code',
    'lv2_name'
]

# 调用api预测传参常量 uat
problemId = int(os.environ.get('PROBLEM_ID'))
taskId = int(os.environ.get('TASK_ID'))
unit_cost_solutionId = int(os.environ.get('UNIT_COST_SOLUTIONID'))
unit_price_solutionId = int(os.environ.get('UNIT_PRICE_SOLUTIONID'))
other_param_solutionId = int(os.environ.get('OTHER_PARAM_SOLUTIONID'))


def check_input(df: pd.DataFrame, cols_to_check: list):
    null_stats = pd.DataFrame({
        '空值数量': df[cols_to_check].isna().sum(),
        '空值占比': df[cols_to_check].isna().mean().round(4) * 100  # 转换为百分比
    })
    if not null_stats.empty:
        log.info(
            f"来源:dm_fop_dimension_lv2_tgt_period_filled_t表,有空值，{null_stats.to_string()}"
        )