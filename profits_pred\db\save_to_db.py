import os
import pandas as pd
import numpy as np
import warnings
from sqlalchemy import MetaData, Table
from profits_pred.src.utils.resource_loader import LOGGER as log, RDB_POOLS
from typing import Union
from datetime import datetime

warnings.filterwarnings("ignore")



cols_should_be_saved_dim = ['period_id', 'time_window_code', 'fcst_type', 'scenarios',
                            'target_period',
                            'bg_code', 'bg_name',
                            'oversea_code', 'oversea_desc',
                            'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name',
                            'currency',
                            'dimension_group_code', 'dimension_group_cn_name', 'dimension_group_en_name',
                            'dimension_subcategory_code', 'dimension_subcategory_cn_name',
                            'dimension_subcategory_en_name',
                            'rev_percent_fcst',
                            'unit_price_fcst_conf', 'unit_price_fcst', 'unit_price_fcst_upper', 'unit_price_fcst_lower',
                            'unit_cost_fcst_conf', 'unit_cost_fcst', 'unit_cost_fcst_upper', 'unit_cost_fcst_lower',
                            'mgp_rate_before_fcst_conf', 'mgp_rate_before_fcst', 'mgp_rate_before_fcst_upper',
                            'mgp_rate_before_fcst_lower',
                            'unit_price_fcst_acc', 'unit_cost_fcst_acc',
                            'carryover_ratio_fcst']

cols_should_be_saved_no_dim = ['period_id', 'time_window_code', 'fcst_type', 'scenarios',
                               'target_period',
                               'bg_code', 'bg_name',
                               'oversea_code', 'oversea_desc',
                               'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name',
                               'currency',
                               'equip_rev_after_fcst_conf', 'equip_rev_after_fcst', 'equip_rev_after_fcst_upper',
                               'equip_rev_after_fcst_lower',
                               'mgp_rate_after_fcst_conf', 'mgp_rate_after_fcst', 'mgp_rate_after_fcst_upper',
                               'mgp_rate_after_fcst_lower',
                               'mca_adjust_ratio_fcst',
                               'mgp_adjust_ratio_fcst']

replace_col = ['carryover_ratio_fcst', 'rev_percent_fcst', 'unit_price_fcst',
               'mgp_rate_before_fcst_upper', 'mgp_rate_after_fcst_lower', 'equip_rev_after_fcst_upper',
               'mgp_rate_after_fcst_conf', 'unit_cost_fcst_upper', 'unit_cost_fcst_acc', 'unit_price_fcst_acc',
               'mgp_rate_after_fcst', 'unit_price_fcst_upper', 'unit_price_fcst_conf', 'unit_cost_fcst_lower',
               'mgp_rate_before_fcst_lower', 'equip_rev_after_fcst', 'mca_adjust_ratio_fcst', 'unit_price_fcst_lower',
               'equip_rev_after_fcst_lower', 'mgp_rate_before_fcst', 'mgp_adjust_ratio_fcst',
               'mgp_rate_before_fcst_conf',
               'unit_cost_fcst', 'mgp_rate_after_fcst_upper', 'unit_cost_fcst_conf', 'equip_rev_after_fcst_conf']


def is_save_enabled() -> bool:
    """检查是否启用数据库保存"""
    return os.environ.get('SAVE_DB_ENABLED', 'true').lower() == 'true'


def save_to_db(df: pd.DataFrame, pred_version: int, scenarios: str = Union['DIM', 'LV2']):
    """
    保存预测结果到数据库
    """

    if scenarios == 'DIM':
        df.rename(columns=lambda col: col.replace('mgp_ratio_', 'mgp_rate_before_')
        if col.startswith('mgp_ratio_') else col, inplace=True)
        df.rename(columns=lambda col: col.replace('carryover_rate_', 'carryover_ratio_')
        if col.startswith('carryover_rate_') else col, inplace=True)
        cols_should_be_saved = cols_should_be_saved_dim
        target_table = 'dm_fop_dimension_fcst_t'
    else:
        df.rename(columns=lambda col: col.replace('mgp_ratio_after_', 'mgp_rate_after_')
        if col.startswith('mgp_ratio_after_') else col, inplace=True)
        df.rename(columns=lambda col: col.replace('equip_rev_cons_after_amt_fcst', 'equip_rev_after_fcst')
        if col.startswith('equip_rev_cons_after_amt_fcst') else col, inplace=True)
        cols_should_be_saved = cols_should_be_saved_no_dim
        target_table = 'dm_fop_dimension_lv2_fcst_t'
    intersection = list(set(cols_should_be_saved) & set(df.columns))
    df = df[intersection]
    df['fcst_type'] = 'YTD法'
    df['target_period'] = pd.to_datetime(df['target_period']).dt.strftime("%Y%m")
    # 加币种
    df['currency'] = 'CNY'
    df['remark'] = ''  # 备注，默认为空字符串
    # 添加 created_by，默认为 842394
    df['created_by'] = 842394
    # 添加 creation_date，默认为当前时间
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    df['creation_date'] = current_time
    # # 添加 last_updated_by，默认为 842394
    df['last_updated_by'] = 842394
    # 添加 last_update_date，默认为当前时间
    df['last_update_date'] = current_time
    # # 添加 del_flag，默认为 'N'
    df['del_flag'] = 'N'
    # 确保其他列的数据类型正确
    # 显式转换数值列，替换 NaN 为 None
    for col in replace_col:
        if col in df.columns:
            # 替换非数值为NaN--再替换为None
            df[col] = pd.to_numeric(df[col], errors='coerce')
            df[col] = df[col].astype(object).replace({np.nan: None})

    if is_save_enabled():
        _insert_to_database(df, target_table)


def _del_pred_version_data(target_table: str, pred_version: int):
    '''
    软删除
    :param target_table:
    :param pred_version:
    :return:
    '''
    try:
        session = next(RDB_POOLS["pg_demo"].get_session_with_commit())
        engine = RDB_POOLS["pg_demo"]._engine
        metadata = MetaData()
        table = Table(target_table, metadata, autoload_with=engine)
        log.info(f"准备软删除 {target_table} 表中-会计期={pred_version} 的数据!")
        session.execute(
            table.update()
            .where(table.c.period_id == pred_version)
            .values({table.c.del_flag: "Y"}))

        session.commit()
        log.info(f"成功软删除 {target_table} 表中-会计期={pred_version} 的数据")
    except Exception as e:
        log.error("软删除{}表-period_id={}数据失败-".format(target_table, e))


def _insert_to_database(df: pd.DataFrame, table_name: str) -> None:
    """插入数据到数据库"""
    try:
        session = next(RDB_POOLS["pg_demo"].get_session_with_commit())

        data_to_insert = df.to_dict(orient='records')
        log.info(f"准备插入 {len(data_to_insert)} 条记录到表 {table_name}")

        engine = RDB_POOLS["pg_demo"]._engine
        metadata = MetaData()
        table = Table(table_name, metadata, autoload_with=engine)

        session.execute(table.insert(), data_to_insert)
        session.commit()
        log.info(f"成功保存 {len(data_to_insert)} 条记录到数据库表 {table_name}")

    except Exception as e:
        session.rollback()
        log.error(f"数据库插入失败 - 表: {table_name}, 错误: {str(e)}")
        raise
