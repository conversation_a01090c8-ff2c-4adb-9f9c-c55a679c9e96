import copy
import json
import os

from slowapi import Limiter
from slowapi.util import get_remote_address

from profits_pred.pyxis.utils.config_loader import Config<PERSON>oader


def load_your_config() -> dict:
    with open(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config", f"{os.environ.get('ENV', 'LOCAL')}.json"), "r", encoding="utf8") as file:
        return json.load(file)


CONFIG_LOADER = ConfigLoader(load_your_config())
CONFIG = copy.deepcopy(CONFIG_LOADER.config)
RDB_POOLS = CONFIG_LOADER.get_rdb_pools()
LOGGER = CONFIG_LOADER.get_logger()
LIMITER = Limiter(
    storage_uri=CONFIG.limiter.storage_uri,
    key_func=get_remote_address,
    strategy=CONFIG.limiter.strategy,
    default_limits=[f"{CONFIG.limiter.frequency}/{CONFIG.limiter.unit}"]
)
