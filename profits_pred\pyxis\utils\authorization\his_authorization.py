import base64
import json
import os

import requests
from aipaas.env import env_mode
from cachetools.func import ttl_cache
from his_decrypt import EncryptType, Environ<PERSON>ey<PERSON>oader, HisDecrypt
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_not_exception_type

from profits_pred.pyxis.utils.config_template import Config


class HisAuthorizationError(Exception):
    def __init__(self):
        super().__init__()




def decrypt(config_parts: list[str], work_key_cipher: str, text: str, encrypt_type: EncryptType) -> str:
    """
    Decrypt the confidential text.
    """
    kl_2 = EnvironKeyLoader("MAIN_KEY", "ASSIST_KEY")
    his_decrypt = HisDecrypt()
    his_decrypt.register(kl_2)
    return his_decrypt.decrypt(
        config_parts=config_parts, work_key_cipher=work_key_cipher,
        password=text, encrypt_type=encrypt_type
    )


@ttl_cache(maxsize=128, ttl=300)
@retry(
    retry=retry_if_not_exception_type(HisAuthorizationError),
    stop=stop_after_attempt(3),
    wait=wait_fixed(1),
    reraise=True
)
def get_dynamic_token_without_param() -> str:
    if str(env_mode).lower() == 'prod':
        api_file = "profits_pred/src/config/PROD-AIF.json"
    else:
        api_file = "profits_pred/src/config/DEV-AIF.json"

    with open(os.path.join(api_file), "r", encoding="utf-8") as f:
        data = json.load(f)

    config = Config(**data)
    url = config.deploy.api_gateway.authorization.url
    app_id = config.deploy.config_server.app_id

    static_token = decrypt(
        config_parts=config.deploy.config_server.config_parts,
        work_key_cipher=os.environ.get("WORK_KEY_CIPHER", ""),
        text=os.environ.get("STATIC_TOKEN", ""),
        encrypt_type=EncryptType.ADV_2_6
    )
    info = {
        "appId": app_id,
        "credential": base64.b64encode(static_token.encode("utf-8")).decode("utf-8")
    }

    response = requests.post(
        url,
        json=info,
        headers={"Content-Type": "application/json"},
        timeout=1
    )
    if response.status_code != 200:
        raise HisAuthorizationError()

    result = response.json()
    dynamic_token = result.get("result")
    if not dynamic_token:
        raise HisAuthorizationError()

    return dynamic_token


@ttl_cache(maxsize=128, ttl=300)
@retry(
    retry=retry_if_not_exception_type(HisAuthorizationError), stop=stop_after_attempt(3),
    wait=wait_fixed(1), reraise=True
)
def get_dynamic_token(url: str, app_id: str, static_token: str) -> str:
    info = {
        "appId": app_id,
        "credential": str(base64.b64encode(bytes(static_token, "utf-8")), "utf-8")
    }

    response = requests.post(
        url,
        json=info, headers={"Content-Type": "application/json"},
        timeout=1
    )
    if response.status_code != 200:
        raise HisAuthorizationError()
    result = json.loads(response.text)
    dynamic_token = result["result"]
    if dynamic_token is None:
        raise HisAuthorizationError()
    return dynamic_token


PROD3_IAM_TOKEN_URL = "https://iam.his-op.huawei.com/iam/auth/token"
PROD2_IAM_TOKEN_URL = "https://iam.heds.huawei.com/iam/auth/token"
BETA2_IAM_TOKEN_URL = "https://iam-icsl.heds-beta2.huawei.com/iam/auth/token"
BETA3_IAM_TOKEN_URL = "https://iam.his-op-beta.huawei.com/iam/auth/token"


@ttl_cache(maxsize=128, ttl=300)
@retry(
    retry=retry_if_not_exception_type(HisAuthorizationError), stop=stop_after_attempt(3),
    wait=wait_fixed(1), reraise=True
)
def get_iam_token(enterprise, project, account, secret, env):
    """
    获取iam动态token
    :return: dynamic token
    """
    if env == 'prod2':  # 蓝版2.0生产
        token_api = PROD2_IAM_TOKEN_URL
    elif env == 'prod3':  # 蓝版3.0生产:
        token_api = PROD3_IAM_TOKEN_URL
    elif env == "beta2":  # 蓝版2.0测试
        token_api = BETA2_IAM_TOKEN_URL
    else:  # 蓝版3.0测试
        token_api = BETA3_IAM_TOKEN_URL

    try:
        data = {
            "data": {"type": "JWT-Token",
                     "attributes": {
                         "method": "CREATE",
                         "account": account,
                         "secret": secret,
                         "project": project,
                         "enterprise": enterprise
                     }
                     }
        }
        response = requests.post(url=token_api, json=data,
                                 headers={'Content-Type': 'application/json'},
                                 verify=False,
                                 timeout=60)
        result = response.json()
        token = result.get("access_token")
        if not token:
            raise ValueError("用户认证信息错误!")
        return token
    except Exception as e:
        raise Exception("获取IAM token失败,{}".format(e)) from e
